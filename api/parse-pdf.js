import { parseResumeFromPDF } from "../config/openai.js";

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { pdfText } = req.body;

  if (!pdfText) {
    return res.status(400).json({ error: "PDF text is required" });
  }

  try {
    const result = await parseResumeFromPDF(pdfText);
    res.json(result);
  } catch (error) {
    console.error("PDF Parse Error:", error.message);
    res.status(500).json({ error: "PDF parsing failed" });
  }
}
