import {
  doc,
  setDoc,
  getDoc,
  updateDoc,
  serverTimestamp,
  collection,
  getDocs,
  deleteDoc,
} from "firebase/firestore";
import { db } from "../firebase";
import { getAuth } from "firebase/auth";

const auth = getAuth();

export const createResume = async (resumeData) => {
  const user = auth.currentUser;
  if (!user) return;

  const resumeRef = doc(db, "users", user.uid, "resume", "data");
  const classicSettingsRef = doc(
    db,
    "users",
    user.uid,
    "classicSettings",
    "data"
  );
  const sidebarSettingsRef = doc(
    db,
    "users",
    user.uid,
    "sidebarSettings",
    "data"
  );
  const standardSettingRef = doc(
    db,
    "users",
    user.uid,
    "standardSettings",
    "data"
  );

  const defaultSettings = {};

  await setDoc(resumeRef, {
    gmail: user.email,
    createdOn: serverTimestamp(),
    updatedOn: serverTimestamp(),
    ...resumeData,
  });

  const [classicSnap, sidebarSnap, standardSnap] = await Promise.all([
    getDoc(classicSettingsRef),
    getDoc(sidebarSettingsRef),
    getDoc(standardSettingRef),
  ]);

  if (!classicSnap.exists()) {
    await setDoc(classicSettingsRef, {
      ...defaultSettings,
      createdOn: serverTimestamp(),
    });
  }

  if (!sidebarSnap.exists()) {
    await setDoc(sidebarSettingsRef, {
      ...defaultSettings,
      createdOn: serverTimestamp(),
    });
  }

  if (!standardSnap.exists()) {
    await setDoc(standardSettingRef, {
      ...defaultSettings,
      createdOn: serverTimestamp(),
    });
  }
};

export const updateResume = async (updatedFields) => {
  const user = auth.currentUser;
  if (!user) return;

  const resumeRef = doc(db, "users", user.uid, "resume", "data");
  await updateDoc(resumeRef, {
    ...updatedFields,
    updatedOn: serverTimestamp(),
  });
};

export const getResumeData = async () => {
  const user = auth.currentUser;
  if (!user) return null;

  const resumeRef = doc(db, "users", user.uid, "resume", "data");
  const snapshot = await getDoc(resumeRef);
  return snapshot.exists() ? snapshot.data() : null;
};

export const editClassicSettings = async (settings) => {
  const user = auth.currentUser;
  if (!user) return;

  const ref = doc(db, "users", user.uid, "classicSettings", "data");
  await setDoc(ref, { ...settings, updatedOn: serverTimestamp() });
};

export const getClassicSettings = async () => {
  const user = auth.currentUser;
  if (!user) return null;

  const ref = doc(db, "users", user.uid, "classicSettings", "data");
  const snapshot = await getDoc(ref);
  return snapshot.exists() ? snapshot.data() : null;
};

export const editSidebarSettings = async (settings) => {
  const user = auth.currentUser;
  if (!user) return;

  const ref = doc(db, "users", user.uid, "sidebarSettings", "data");
  await setDoc(ref, { ...settings, updatedOn: serverTimestamp() });
};

export const getSidebarSettings = async () => {
  const user = auth.currentUser;
  if (!user) return null;

  const ref = doc(db, "users", user.uid, "sidebarSettings", "data");
  const snapshot = await getDoc(ref);
  return snapshot.exists() ? snapshot.data() : null;
};

export const editStandardSettings = async (settings) => {
  const user = auth.currentUser;
  if (!user) return;

  const ref = doc(db, "users", user.uid, "standardSettings", "data");
  await setDoc(ref, { ...settings, updatedOn: serverTimestamp() });
};

export const getStandardSettings = async () => {
  const user = auth.currentUser;
  if (!user) return null;

  const ref = doc(db, "users", user.uid, "standardSettings", "data");
  const snapshot = await getDoc(ref);
  return snapshot.exists() ? snapshot.data() : null;
};

// Store uploaded resume metadata and parsed data
export const saveUploadedResume = async (uploadData) => {
  const user = auth.currentUser;
  if (!user) return;

  const uploadedResumeRef = doc(
    db,
    "users",
    user.uid,
    "uploadedResumes",
    uploadData.fileId
  );

  await setDoc(uploadedResumeRef, {
    fileId: uploadData.fileId,
    fileName: uploadData.fileName,
    fileUrl: uploadData.fileUrl,
    action: uploadData.action,
    parsedData: uploadData.parsedData,
    extractedText: uploadData.extractedText,
    uploadedAt: serverTimestamp(),
    createdBy: user.email,
  });
};

// Get all uploaded resumes for the current user
export const getUploadedResumes = async () => {
  const user = auth.currentUser;
  if (!user) return [];

  const uploadedResumesRef = collection(
    db,
    "users",
    user.uid,
    "uploadedResumes"
  );
  const snapshot = await getDocs(uploadedResumesRef);

  return snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));
};

// Get a specific uploaded resume
export const getUploadedResume = async (fileId) => {
  const user = auth.currentUser;
  if (!user) return null;

  const uploadedResumeRef = doc(
    db,
    "users",
    user.uid,
    "uploadedResumes",
    fileId
  );
  const snapshot = await getDoc(uploadedResumeRef);

  return snapshot.exists() ? snapshot.data() : null;
};

// Delete an uploaded resume
export const deleteUploadedResume = async (fileId) => {
  const user = auth.currentUser;
  if (!user) return;

  const uploadedResumeRef = doc(
    db,
    "users",
    user.uid,
    "uploadedResumes",
    fileId
  );
  await deleteDoc(uploadedResumeRef);
};
