import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaUpload, FaRocket, FaCheckCircle } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import PDFUploader from '../Components/PDFUploader';
import UploadedResumesSection from '../Components/UploadedResumesSection';
import { ResumeParserService } from '../services/resumeParserService';
import { useResumeData } from '../Contexts/ResumeDataContext';
import toast from 'react-hot-toast';

const UploadResume = () => {
  const navigate = useNavigate();
  const { setResume } = useResumeData();
  const [showUploader, setShowUploader] = useState(false);
  const [processing, setProcessing] = useState(false);

  const handleUploadSuccess = async (uploadInfo) => {
    setProcessing(true);
    try {
      const result = await ResumeParserService.processUploadedPDF(uploadInfo);
      
      if (result.success) {
        toast.success(result.message);
        
        // If creating a resume, update the context
        if (result.action === 'create-resume') {
          setResume(result.data);
        }
        
        // Navigate to appropriate page
        if (result.redirectTo) {
          navigate(result.redirectTo);
        }
      }
    } catch (error) {
      console.error('Upload processing error:', error);
      toast.error('Failed to process uploaded resume. Please try again.');
    } finally {
      setProcessing(false);
      setShowUploader(false);
    }
  };

  const features = [
    {
      icon: FaRocket,
      title: 'AI-Powered Parsing',
      description: 'Our advanced AI extracts and structures your resume data automatically'
    },
    {
      icon: FaCheckCircle,
      title: 'Multiple Actions',
      description: 'Create new resume, check ATS compatibility, or analyze job fit'
    },
    {
      icon: FaUpload,
      title: 'Easy Upload',
      description: 'Simply drag and drop your PDF resume or click to browse'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-sky-50 to-white p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center gap-2 bg-white/60 backdrop-blur-sm border border-white/40 rounded-full px-4 py-1.5 mb-6 shadow-lg">
            <FaUpload className="text-blue-500 text-sm" />
            <span className="text-sm font-medium text-slate-700">
              Resume Upload
            </span>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Upload Your Resume
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Transform your existing resume with AI-powered analysis and optimization
          </p>
        </motion.div>

        {/* Features */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid md:grid-cols-3 gap-6 mb-12"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1 }}
              className="bg-white/80 backdrop-blur-sm border border-white/20 rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg text-white">
                  <feature.icon className="text-xl" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800">
                  {feature.title}
                </h3>
              </div>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* Upload Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="text-center mb-12"
        >
          <motion.button
            onClick={() => setShowUploader(true)}
            disabled={processing}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-500 text-white rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FaUpload className="text-xl" />
            {processing ? 'Processing...' : 'Upload Resume PDF'}
          </motion.button>
        </motion.div>

        {/* Uploaded Resumes Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <UploadedResumesSection />
        </motion.div>

        {/* PDF Uploader Modal */}
        {showUploader && (
          <PDFUploader
            onUploadSuccess={handleUploadSuccess}
            onClose={() => setShowUploader(false)}
          />
        )}
      </div>
    </div>
  );
};

export default UploadResume;
