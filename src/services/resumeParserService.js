import { processPDFUpload } from "../utils/pdfParser";
import {
  saveUploadedResume,
  createResume,
  deleteUploadedResume,
} from "../db/database";
import { deleteFile } from "../config/appwrite";
import toast from "react-hot-toast";

// Main service to handle PDF upload and processing
export class ResumeParserService {
  // Process uploaded PDF based on selected action
  static async processUploadedPDF(uploadInfo) {
    try {
      // Process the PDF and extract structured data
      const processedData = await processPDFUpload(
        uploadInfo.fileUrl,
        uploadInfo.action
      );

      // Save the uploaded resume metadata to database
      await saveUploadedResume({
        ...uploadInfo,
        ...processedData,
      });

      // Handle different actions
      switch (uploadInfo.action) {
        case "create-resume":
          return await this.handleCreateResume(processedData);
        case "ats-check":
          return await this.handleATSCheck(processedData);
        case "job-description":
          return await this.handleJobDescriptionMatch(processedData);
        default:
          throw new Error("Invalid action specified");
      }
    } catch (error) {
      console.error("Error processing uploaded PDF:", error);
      // Clean up uploaded file on error
      try {
        await deleteFile(uploadInfo.fileId);
      } catch (cleanupError) {
        console.error("Error cleaning up file:", cleanupError);
      }
      throw error;
    }
  }

  // Handle create resume action
  static async handleCreateResume(processedData) {
    try {
      // Save the parsed data as a new resume
      await createResume(processedData.parsedData);

      return {
        success: true,
        action: "create-resume",
        message: "Resume created successfully from uploaded PDF!",
        data: processedData.parsedData,
        redirectTo: "/resume",
      };
    } catch (error) {
      console.error("Error creating resume from PDF:", error);
      throw new Error("Failed to create resume from uploaded PDF");
    }
  }

  // Handle ATS check action
  static async handleATSCheck(processedData) {
    try {
      // For ATS check, we'll redirect to the ATS checker with the parsed data
      return {
        success: true,
        action: "ats-check",
        message: "PDF processed successfully! Redirecting to ATS checker...",
        data: processedData.parsedData,
        redirectTo: "/ats-compatibility-checker",
      };
    } catch (error) {
      console.error("Error preparing ATS check:", error);
      throw new Error("Failed to prepare ATS compatibility check");
    }
  }

  // Handle job description matching action
  static async handleJobDescriptionMatch(processedData) {
    try {
      // For job description matching, redirect to job fit analyzer
      return {
        success: true,
        action: "job-description",
        message:
          "PDF processed successfully! Redirecting to job fit analyzer...",
        data: processedData.parsedData,
        redirectTo: "/job-fit-analyzer",
      };
    } catch (error) {
      console.error("Error preparing job description match:", error);
      throw new Error("Failed to prepare job description matching");
    }
  }

  // Validate parsed resume data structure
  static validateResumeData(data) {
    const requiredFields = [
      "name",
      "education",
      "skills",
      "projects",
      "experience",
      "achievements",
      "contact",
    ];

    for (const field of requiredFields) {
      if (!data.hasOwnProperty(field)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate specific field structures
    if (!Array.isArray(data.skills)) {
      throw new Error("Skills must be an array");
    }

    if (!Array.isArray(data.projects)) {
      throw new Error("Projects must be an array");
    }

    if (!Array.isArray(data.experience)) {
      throw new Error("Experience must be an array");
    }

    if (!Array.isArray(data.achievements)) {
      throw new Error("Achievements must be an array");
    }

    if (typeof data.contact !== "object") {
      throw new Error("Contact must be an object");
    }

    if (typeof data.education !== "object") {
      throw new Error("Education must be an object");
    }

    return true;
  }

  // Clean up uploaded file and database entry
  static async cleanupUploadedResume(fileId) {
    try {
      // Delete from Appwrite storage
      await deleteFile(fileId);

      // Delete from database
      await deleteUploadedResume(fileId);

      return true;
    } catch (error) {
      console.error("Error cleaning up uploaded resume:", error);
      throw error;
    }
  }

  // Get processing status message based on action
  static getProcessingMessage(action) {
    switch (action) {
      case "create-resume":
        return "Processing PDF and creating your resume...";
      case "ats-check":
        return "Analyzing PDF for ATS compatibility...";
      case "job-description":
        return "Preparing PDF for job description matching...";
      default:
        return "Processing your PDF...";
    }
  }
}
