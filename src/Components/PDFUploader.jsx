import React, { useState, useRef } from "react";
import { motion } from "framer-motion";
import {
  FaUpload,
  FaFilePdf,
  FaTimes,
  FaRobot,
  FaSearch,
  FaBriefcase,
} from "react-icons/fa";
import { uploadPDF, getFileURL } from "../config/appwrite";
import toast from "react-hot-toast";

const PDFUploader = ({ onUploadSuccess, onClose }) => {
  const [file, setFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [selectedAction, setSelectedAction] = useState("");
  const fileInputRef = useRef(null);

  const handleFileSelect = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      if (selectedFile.type !== "application/pdf") {
        toast.error("Please select a PDF file");
        return;
      }
      if (selectedFile.size > 10 * 1024 * 1024) {
        // 10MB limit
        toast.error("File size should be less than 10MB");
        return;
      }
      setFile(selectedFile);
    }
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile) {
      if (droppedFile.type !== "application/pdf") {
        toast.error("Please select a PDF file");
        return;
      }
      if (droppedFile.size > 10 * 1024 * 1024) {
        toast.error("File size should be less than 10MB");
        return;
      }
      setFile(droppedFile);
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const handleUpload = async () => {
    if (!file || !selectedAction) {
      toast.error("Please select a file and choose an action");
      return;
    }

    setUploading(true);
    try {
      // Upload file to Appwrite
      const uploadResponse = await uploadPDF(file);
      const fileUrl = getFileURL(uploadResponse.$id);

      // Call the success callback with file info and selected action
      onUploadSuccess({
        fileId: uploadResponse.$id,
        fileName: file.name,
        fileUrl: fileUrl.toString(), // Convert URL to string
        action: selectedAction,
        uploadedAt: new Date().toISOString(),
      });

      toast.success("PDF uploaded successfully!");
      onClose();
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Failed to upload PDF. Please try again.");
    } finally {
      setUploading(false);
    }
  };

  const removeFile = () => {
    setFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const actionOptions = [
    {
      id: "create-resume",
      title: "Create Resume",
      description: "Parse PDF and create a new resume",
      icon: FaRobot,
      color: "from-blue-500 to-cyan-500",
    },
    {
      id: "ats-check",
      title: "ATS Compatibility Check",
      description: "Analyze resume for ATS compatibility",
      icon: FaSearch,
      color: "from-green-500 to-emerald-500",
    },
    {
      id: "job-description",
      title: "Job Description Matching",
      description: "Compare resume with job descriptions",
      icon: FaBriefcase,
      color: "from-purple-500 to-pink-500",
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800">
            Upload Resume PDF
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <FaTimes className="text-gray-500" />
          </button>
        </div>

        {/* File Upload Area */}
        <div className="mb-6">
          <div
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept=".pdf"
              onChange={handleFileSelect}
              className="hidden"
            />

            {file ? (
              <div className="flex items-center justify-center space-x-3">
                <FaFilePdf className="text-red-500 text-3xl" />
                <div className="text-left">
                  <p className="font-medium text-gray-800">{file.name}</p>
                  <p className="text-sm text-gray-500">
                    {(file.size / (1024 * 1024)).toFixed(2)} MB
                  </p>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removeFile();
                  }}
                  className="p-1 hover:bg-gray-100 rounded-full"
                >
                  <FaTimes className="text-gray-400" />
                </button>
              </div>
            ) : (
              <div>
                <FaUpload className="text-gray-400 text-4xl mx-auto mb-4" />
                <p className="text-gray-600 mb-2">
                  Drag and drop your PDF here, or click to browse
                </p>
                <p className="text-sm text-gray-400">Maximum file size: 10MB</p>
              </div>
            )}
          </div>
        </div>

        {/* Action Selection */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            What would you like to do with this resume?
          </h3>
          <div className="grid gap-3">
            {actionOptions.map((option) => (
              <motion.div
                key={option.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`p-4 border-2 rounded-xl cursor-pointer transition-all ${
                  selectedAction === option.id
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setSelectedAction(option.id)}
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={`p-2 rounded-lg bg-gradient-to-r ${option.color}`}
                  >
                    <option.icon className="text-white text-lg" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-800">
                      {option.title}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {option.description}
                    </p>
                  </div>
                  <div
                    className={`w-4 h-4 rounded-full border-2 ${
                      selectedAction === option.id
                        ? "border-blue-500 bg-blue-500"
                        : "border-gray-300"
                    }`}
                  >
                    {selectedAction === option.id && (
                      <div className="w-2 h-2 bg-white rounded-full m-0.5" />
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Upload Button */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <motion.button
            onClick={handleUpload}
            disabled={!file || !selectedAction || uploading}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="px-6 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-all"
          >
            {uploading ? "Uploading..." : "Upload & Process"}
          </motion.button>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default PDFUploader;
