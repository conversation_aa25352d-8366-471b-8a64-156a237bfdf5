import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaFilePdf, FaDownload, FaTrash, FaEye, FaClock, FaRobot, FaSearch, FaBriefcase } from 'react-icons/fa';
import { getUploadedResumes, deleteUploadedResume } from '../db/database';
import { deleteFile } from '../config/appwrite';
import toast from 'react-hot-toast';

const UploadedResumesSection = () => {
  const [uploadedResumes, setUploadedResumes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(null);

  useEffect(() => {
    loadUploadedResumes();
  }, []);

  const loadUploadedResumes = async () => {
    try {
      const resumes = await getUploadedResumes();
      setUploadedResumes(resumes);
    } catch (error) {
      console.error('Error loading uploaded resumes:', error);
      toast.error('Failed to load uploaded resumes');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (resume) => {
    if (!window.confirm('Are you sure you want to delete this uploaded resume?')) {
      return;
    }

    setDeleting(resume.fileId);
    try {
      // Delete from storage
      await deleteFile(resume.fileId);
      // Delete from database
      await deleteUploadedResume(resume.fileId);
      
      // Update local state
      setUploadedResumes(prev => prev.filter(r => r.fileId !== resume.fileId));
      toast.success('Resume deleted successfully');
    } catch (error) {
      console.error('Error deleting resume:', error);
      toast.error('Failed to delete resume');
    } finally {
      setDeleting(null);
    }
  };

  const getActionIcon = (action) => {
    switch (action) {
      case 'create-resume':
        return <FaRobot className="text-blue-500" />;
      case 'ats-check':
        return <FaSearch className="text-green-500" />;
      case 'job-description':
        return <FaBriefcase className="text-purple-500" />;
      default:
        return <FaFilePdf className="text-red-500" />;
    }
  };

  const getActionLabel = (action) => {
    switch (action) {
      case 'create-resume':
        return 'Create Resume';
      case 'ats-check':
        return 'ATS Check';
      case 'job-description':
        return 'Job Matching';
      default:
        return 'Unknown';
    }
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'Unknown';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-100 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (uploadedResumes.length === 0) {
    return (
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Uploaded Resumes</h3>
        <div className="text-center py-8">
          <FaFilePdf className="text-gray-300 text-4xl mx-auto mb-4" />
          <p className="text-gray-500">No uploaded resumes yet</p>
          <p className="text-sm text-gray-400 mt-2">
            Upload a PDF resume to get started with AI-powered analysis
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">
        Uploaded Resumes ({uploadedResumes.length})
      </h3>
      
      <div className="space-y-3">
        {uploadedResumes.map((resume) => (
          <motion.div
            key={resume.fileId}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 flex-1">
                <div className="flex-shrink-0">
                  {getActionIcon(resume.action)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium text-gray-800 truncate">
                      {resume.fileName}
                    </h4>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {getActionLabel(resume.action)}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <FaClock className="text-xs" />
                      <span>{formatDate(resume.uploadedAt)}</span>
                    </div>
                    {resume.parsedData?.name && (
                      <span>• {resume.parsedData.name}</span>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => window.open(resume.fileUrl, '_blank')}
                  className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  title="View PDF"
                >
                  <FaEye />
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    const link = document.createElement('a');
                    link.href = resume.fileUrl;
                    link.download = resume.fileName;
                    link.click();
                  }}
                  className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                  title="Download PDF"
                >
                  <FaDownload />
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleDelete(resume)}
                  disabled={deleting === resume.fileId}
                  className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50"
                  title="Delete"
                >
                  {deleting === resume.fileId ? (
                    <div className="animate-spin w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full"></div>
                  ) : (
                    <FaTrash />
                  )}
                </motion.button>
              </div>
            </div>

            {/* Show parsed data preview if available */}
            {resume.parsedData && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  {resume.parsedData.contact?.email && (
                    <div>
                      <span className="text-gray-500">Email:</span>
                      <p className="font-medium truncate">{resume.parsedData.contact.email}</p>
                    </div>
                  )}
                  {resume.parsedData.contact?.phone && (
                    <div>
                      <span className="text-gray-500">Phone:</span>
                      <p className="font-medium">{resume.parsedData.contact.phone}</p>
                    </div>
                  )}
                  {resume.parsedData.education?.college && (
                    <div>
                      <span className="text-gray-500">Education:</span>
                      <p className="font-medium truncate">{resume.parsedData.education.college}</p>
                    </div>
                  )}
                  {resume.parsedData.experience?.length > 0 && (
                    <div>
                      <span className="text-gray-500">Experience:</span>
                      <p className="font-medium">{resume.parsedData.experience.length} positions</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default UploadedResumesSection;
