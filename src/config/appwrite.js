// src/config/appwrite.js
import { Client, Storage, ID } from "appwrite";

const client = new Client();

client
  .setEndpoint(
    import.meta.env.VITE_APPWRITE_ENDPOINT || "https://cloud.appwrite.io/v1"
  )
  .setProject(import.meta.env.VITE_APPWRITE_PROJECT_ID);

export const storage = new Storage(client);

// Storage bucket ID for resume PDFs
export const RESUME_BUCKET_ID =
  import.meta.env.VITE_APPWRITE_BUCKET_ID || "resume-pdfs";

// Upload PDF file to Appwrite storage
export const uploadPDF = async (file) => {
  try {
    const fileId = ID.unique();
    const response = await storage.createFile(RESUME_BUCKET_ID, fileId, file);
    return response;
  } catch (error) {
    console.error("Error uploading PDF:", error);
    throw error;
  }
};

// Get file URL from Appwrite storage
export const getFileURL = (fileId) => {
  try {
    const url = storage.getFileView(RESUME_BUCKET_ID, fileId);
    return url.toString(); // Return as string instead of URL object
  } catch (error) {
    console.error("Error getting file URL:", error);
    throw error;
  }
};

// Delete file from Appwrite storage
export const deleteFile = async (fileId) => {
  try {
    await storage.deleteFile(RESUME_BUCKET_ID, fileId);
    return true;
  } catch (error) {
    console.error("Error deleting file:", error);
    throw error;
  }
};

export { client };
