import { extractTextFromPDF, parseResumeData, processPDFUpload } from '../pdfParser.js';

// Mock PDF.js
jest.mock('pdfjs-dist', () => ({
  GlobalWorkerOptions: { workerSrc: '' },
  getDocument: jest.fn(() => ({
    promise: Promise.resolve({
      numPages: 1,
      getPage: jest.fn(() => Promise.resolve({
        getTextContent: jest.fn(() => Promise.resolve({
          items: [
            { str: 'John Doe' },
            { str: 'Software Engineer' },
            { str: 'Experience: 5 years' }
          ]
        }))
      }))
    })
  }))
}));

// Mock fetch
global.fetch = jest.fn();

describe('PDF Parser', () => {
  beforeEach(() => {
    fetch.mockClear();
  });

  describe('extractTextFromPDF', () => {
    it('should extract text from PDF successfully', async () => {
      // Mock successful PDF fetch
      fetch.mockResolvedValueOnce({
        ok: true,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024))
      });

      const result = await extractTextFromPDF('http://example.com/test.pdf');
      
      expect(result).toBe('John Doe Software Engineer Experience: 5 years');
      expect(fetch).toHaveBeenCalledWith('http://example.com/test.pdf', {
        method: 'GET',
        headers: { Accept: 'application/pdf' },
        mode: 'cors'
      });
    });

    it('should handle fetch errors', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      await expect(extractTextFromPDF('http://example.com/missing.pdf'))
        .rejects.toThrow('Failed to fetch PDF: 404 Not Found');
    });

    it('should handle empty PDF', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(0))
      });

      await expect(extractTextFromPDF('http://example.com/empty.pdf'))
        .rejects.toThrow('PDF file is empty or corrupted');
    });
  });

  describe('parseResumeData', () => {
    it('should parse PDF text successfully', async () => {
      const mockResponse = {
        personalInfo: { name: 'John Doe', email: '<EMAIL>' },
        experience: [{ company: 'ABC Corp', position: 'Developer' }]
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await parseResumeData('John Doe\nSoftware Engineer');
      
      expect(result).toEqual(mockResponse);
      expect(fetch).toHaveBeenCalledWith('/api/parse-pdf', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pdfText: 'John Doe\nSoftware Engineer' })
      });
    });
  });

  describe('processPDFUpload', () => {
    it('should process PDF upload successfully', async () => {
      // Mock PDF extraction
      fetch.mockResolvedValueOnce({
        ok: true,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024))
      });

      // Mock parsing API
      const mockParsedData = {
        personalInfo: { name: 'John Doe' },
        experience: []
      };
      
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockParsedData)
      });

      const result = await processPDFUpload('http://example.com/resume.pdf', 'create-resume');
      
      expect(result).toEqual({
        action: 'create-resume',
        fileUrl: 'http://example.com/resume.pdf',
        parsedData: mockParsedData,
        extractedText: 'John Doe Software Engineer Experience: 5 years'
      });
    });

    it('should handle empty extracted text', async () => {
      // Mock PDF extraction returning empty text
      fetch.mockResolvedValueOnce({
        ok: true,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024))
      });

      // Mock PDF.js to return empty text
      const pdfjsLib = require('pdfjs-dist');
      pdfjsLib.getDocument.mockReturnValueOnce({
        promise: Promise.resolve({
          numPages: 1,
          getPage: jest.fn(() => Promise.resolve({
            getTextContent: jest.fn(() => Promise.resolve({
              items: []
            }))
          }))
        })
      });

      await expect(processPDFUpload('http://example.com/empty.pdf', 'create-resume'))
        .rejects.toThrow('No text could be extracted from the PDF');
    });
  });
});
