import * as pdfjsLib from "pdfjs-dist";

// Set up PDF.js worker with fallback
// Try local worker first, fallback to CDN if needed
if (typeof window !== "undefined") {
  pdfjsLib.GlobalWorkerOptions.workerSrc = "/js/pdf.worker.min.js";
} else {
  // Fallback for server-side rendering or if local worker fails
  pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.mjs`;
}

const API_BASE = import.meta.env.VITE_API_BASE || "/api";

// Initialize PDF.js with error handling
let pdfWorkerInitialized = false;

async function initializePDFWorker() {
  if (pdfWorkerInitialized) return;

  try {
    // Test if the worker loads correctly with a minimal PDF header
    await pdfjsLib.getDocument({
      data: new Uint8Array([37, 80, 68, 70]), // "%PDF" header
    }).promise;
    pdfWorkerInitialized = true;
    console.log("PDF worker initialized successfully");
  } catch (error) {
    console.warn("Local PDF worker failed, trying CDN fallback:", error);

    // Try multiple CDN fallbacks
    const fallbackUrls = [
      `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.mjs`,
      `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.mjs`,
      `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`,
    ];

    for (const url of fallbackUrls) {
      try {
        pdfjsLib.GlobalWorkerOptions.workerSrc = url;
        await pdfjsLib.getDocument({
          data: new Uint8Array([37, 80, 68, 70]),
        }).promise;
        console.log(`PDF worker initialized with fallback: ${url}`);
        pdfWorkerInitialized = true;
        return;
      } catch (fallbackError) {
        console.warn(`Fallback ${url} failed:`, fallbackError);
      }
    }

    throw new Error("All PDF worker sources failed to load");
  }
}

// Extract text from PDF URL using PDF.js
export async function extractTextFromPDF(pdfUrl) {
  try {
    console.log("Starting PDF text extraction from:", pdfUrl);

    // Initialize PDF worker if not already done
    await initializePDFWorker();

    // Fetch the PDF file with proper headers for CORS
    const response = await fetch(pdfUrl, {
      method: "GET",
      headers: {
        Accept: "application/pdf",
      },
      mode: "cors",
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch PDF: ${response.status} ${response.statusText}`
      );
    }

    const arrayBuffer = await response.arrayBuffer();

    if (arrayBuffer.byteLength === 0) {
      throw new Error("PDF file is empty or corrupted");
    }

    // Load the PDF document
    const pdf = await pdfjsLib.getDocument({
      data: arrayBuffer,
      // Disable font loading to avoid potential issues
      disableFontFace: true,
      // Set maximum allowed image size
      maxImageSize: 1024 * 1024,
      // Disable range requests for better compatibility
      disableRange: true,
      // Disable streaming for better compatibility
      disableStream: true,
    }).promise;

    console.log(`PDF loaded successfully. Number of pages: ${pdf.numPages}`);

    let fullText = "";

    // Extract text from each page
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Combine text items with better formatting
        let pageText = "";
        let lastY = null;

        textContent.items.forEach((item, index) => {
          const currentY = item.transform[5]; // Y coordinate

          // Add line break if Y coordinate changed significantly (new line)
          if (lastY !== null && Math.abs(currentY - lastY) > 5) {
            pageText += "\n";
          }

          // Add space before text if not at start of line
          if (index > 0 && item.str.trim() && !pageText.endsWith("\n")) {
            pageText += " ";
          }

          pageText += item.str;
          lastY = currentY;
        });

        pageText = pageText.trim();

        if (pageText) {
          fullText += pageText + "\n\n";
        }

        console.log(
          `Extracted text from page ${pageNum}: ${pageText.length} characters`
        );
      } catch (pageError) {
        console.warn(`Failed to extract text from page ${pageNum}:`, pageError);
        // Continue with other pages even if one fails
      }
    }

    if (fullText.trim().length === 0) {
      throw new Error(
        "No readable text found in the PDF. The PDF might be image-based or corrupted."
      );
    }

    console.log(
      "PDF text extraction completed successfully. Total characters:",
      fullText.length
    );
    return fullText.trim();
  } catch (error) {
    console.error("PDF text extraction error:", error);

    // Provide more specific error messages
    if (
      error.message.includes("worker") ||
      error.message.includes("fake worker")
    ) {
      throw new Error(
        "PDF processing service is temporarily unavailable. Please try again in a moment."
      );
    } else if (error.message.includes("CORS")) {
      throw new Error(
        "Unable to access PDF due to security restrictions. Please try uploading the file again."
      );
    } else if (error.message.includes("fetch")) {
      throw new Error(
        "Unable to download PDF file. Please check your internet connection and try again."
      );
    } else if (
      error.message.includes("Invalid PDF") ||
      error.message.includes("Invalid header")
    ) {
      throw new Error("The uploaded file is not a valid PDF or is corrupted.");
    } else if (
      error.message.includes("password") ||
      error.message.includes("encrypted")
    ) {
      throw new Error(
        "This PDF is password-protected. Please upload an unprotected version."
      );
    } else if (error.message.includes("image-based")) {
      throw new Error(
        "This PDF appears to contain only images. Please upload a PDF with selectable text."
      );
    }

    throw new Error(`Failed to extract text from PDF: ${error.message}`);
  }
}

// Parse extracted PDF text into structured resume data
export async function parseResumeData(pdfText) {
  try {
    const res = await fetch(`${API_BASE}/parse-pdf`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ pdfText }),
    });

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    const data = await res.json();
    return data;
  } catch (err) {
    console.error("Resume parsing error:", err);
    throw err;
  }
}

// Process uploaded PDF file and return structured data
export async function processPDFUpload(fileUrl, action) {
  try {
    console.log("Processing PDF upload:", { fileUrl, action });

    // Extract text from the uploaded PDF
    const extractedText = await extractTextFromPDF(fileUrl);

    if (!extractedText || extractedText.trim().length === 0) {
      throw new Error(
        "No text could be extracted from the PDF. Please ensure the PDF contains readable text."
      );
    }

    console.log("Extracted text length:", extractedText.length);

    // Parse the extracted text into structured resume data
    const parsedData = await parseResumeData(extractedText);

    return {
      action,
      fileUrl,
      parsedData,
      extractedText,
    };
  } catch (error) {
    console.error("PDF processing error:", error);
    throw new Error(`Failed to process PDF: ${error.message}`);
  }
}
